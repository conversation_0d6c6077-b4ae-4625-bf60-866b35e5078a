const axios = require('axios');
const AiLog = require('../models/AiLog');
require('dotenv').config();

/**
 * AI Controller for handling doctor specialty predictions
 * Uses OpenRouter API to analyze symptoms and suggest appropriate medical specialties
 */

/**
 * Predict doctor specialty based on symptoms
 * POST /api/ai/specialty
 */
const predictSpecialty = async (req, res) => {
  const startTime = Date.now();
  let logData = {
    symptoms: '',
    predictedSpecialty: '',
    responseTime: 0,
    success: false,
    ipAddress: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent')
  };

  try {
    // Validate request body
    const { symptoms } = req.body;
    
    if (!symptoms || typeof symptoms !== 'string' || symptoms.trim().length === 0) {
      return res.status(400).json({
        error: 'Symptoms are required and must be a non-empty string'
      });
    }

    logData.symptoms = symptoms.trim();

    // Prepare the prompt for OpenRouter API
    const prompt = `A patient describes their symptoms as:
"${symptoms.trim()}"
Based on this, suggest the most suitable doctor specialty.
Only respond with the specialty name.`;

    console.log('Making request to OpenRouter API...');
    
    // Make request to OpenRouter API
    const openRouterResponse = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model: process.env.OPENROUTER_MODEL,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 50,
        temperature: 0.3
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'http://localhost:3000',
          'X-Title': 'Medical Appointment System'
        },
        timeout: 30000 // 30 second timeout
      }
    );

    // Extract the specialty from the response
    const specialty = openRouterResponse.data.choices[0].message.content.trim();
    
    // Calculate response time
    const responseTime = Date.now() - startTime;
    
    // Update log data
    logData.predictedSpecialty = specialty;
    logData.responseTime = responseTime;
    logData.success = true;

    // Log the request and response to MongoDB
    try {
      await AiLog.createLog(logData);
      console.log('AI request logged successfully');
    } catch (logError) {
      console.error('Error logging AI request:', logError);
      // Don't fail the request if logging fails
    }

    // Send successful response
    res.json({
      specialty: specialty
    });

    console.log(`Specialty prediction successful: ${specialty} (${responseTime}ms)`);

  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    console.error('Error in predictSpecialty:', error.message);
    
    // Update log data for failed request
    logData.responseTime = responseTime;
    logData.success = false;
    logData.error = error.message;

    // Log the failed request
    try {
      await AiLog.createLog(logData);
    } catch (logError) {
      console.error('Error logging failed AI request:', logError);
    }

    // Handle different types of errors
    if (error.code === 'ECONNABORTED') {
      return res.status(408).json({
        error: 'Request timeout - AI service took too long to respond'
      });
    }

    if (error.response) {
      // OpenRouter API returned an error
      const status = error.response.status;
      const message = error.response.data?.error?.message || 'AI service error';
      
      return res.status(status >= 400 && status < 500 ? status : 500).json({
        error: `AI service error: ${message}`
      });
    }

    if (error.request) {
      // Network error
      return res.status(503).json({
        error: 'Unable to connect to AI service'
      });
    }

    // Generic server error
    res.status(500).json({
      error: 'Internal server error while processing your request'
    });
  }
};

/**
 * Get AI logs (for debugging/analytics)
 * GET /api/ai/logs
 */
const getAiLogs = async (req, res) => {
  try {
    const { limit = 10, page = 1, success } = req.query;
    
    const query = {};
    if (success !== undefined) {
      query.success = success === 'true';
    }

    const logs = await AiLog.find(query)
      .sort({ requestTimestamp: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .select('-__v');

    const total = await AiLog.countDocuments(query);

    res.json({
      logs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching AI logs:', error);
    res.status(500).json({
      error: 'Error fetching AI logs'
    });
  }
};

module.exports = {
  predictSpecialty,
  getAiLogs
};
