const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Import configurations and middleware
const connectDB = require('./config/database');
const errorHandler = require('./middleware/errorHandler');
const requestLogger = require('./middleware/requestLogger');

// Import routes
const routes = require('./routes');

/**
 * Medical Appointment System Backend
 * Node.js + Express server with AI-powered doctor specialty recommendations
 */

// Initialize Express app
const app = express();

// Connect to MongoDB
connectDB();

// Middleware setup
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com'] // Replace with your production domain
    : ['http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:3000'],
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Request logging middleware (only in development)
if (process.env.NODE_ENV === 'development') {
  app.use(requestLogger);
}

// API routes
app.use('/api', routes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Medical Appointment System API',
    version: '1.0.0',
    status: 'Running',
    endpoints: {
      health: '/api/health',
      aiSpecialty: 'POST /api/ai/specialty',
      aiLogs: 'GET /api/ai/logs'
    },
    documentation: {
      aiSpecialty: {
        method: 'POST',
        endpoint: '/api/ai/specialty',
        description: 'Predict doctor specialty based on symptoms',
        requestBody: {
          symptoms: 'string (required) - Patient symptoms description'
        },
        response: {
          specialty: 'string - Recommended doctor specialty'
        },
        example: {
          request: {
            symptoms: 'I have a rash and itchy skin around my arms and neck.'
          },
          response: {
            specialty: 'Dermatologist'
          }
        }
      }
    }
  });
});

// Handle 404 errors
app.use((req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    availableEndpoints: [
      'GET /',
      'GET /api/health',
      'POST /api/ai/specialty',
      'GET /api/ai/logs'
    ]
  });
});

// Global error handler (must be last)
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 3000;

const server = app.listen(PORT, () => {
  console.log(`
🚀 Medical Appointment System Backend Started!
📍 Server running on port ${PORT}
🌍 Environment: ${process.env.NODE_ENV || 'development'}
📊 MongoDB: Connected
🤖 AI Service: OpenRouter (${process.env.OPENROUTER_MODEL})

📋 Available Endpoints:
   GET  /                    - API documentation
   GET  /api/health          - Health check
   POST /api/ai/specialty    - AI specialty prediction
   GET  /api/ai/logs         - AI request logs

🔧 To test the AI endpoint:
   curl -X POST http://localhost:${PORT}/api/ai/specialty \\
        -H "Content-Type: application/json" \\
        -d '{"symptoms": "I have a headache and fever"}'
  `);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  server.close(() => {
    console.log('Process terminated');
  });
});

module.exports = app;
